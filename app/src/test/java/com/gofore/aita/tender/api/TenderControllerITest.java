package com.gofore.aita.tender.api;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.gofore.aita.BaseTest;
import com.gofore.aita.tender.api.dto.FileMetadataDTO;
import com.gofore.aita.tender.api.dto.TenderDTO;
import com.gofore.aita.tender.api.dto.UserDTO;
import com.gofore.aita.tender.api.util.CredentialHelper;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.utils.TestUtils;
import com.mongodb.client.MongoClient;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.util.MultiValueMap;

/** Integration tests for the TenderController class. */
public class TenderControllerITest extends BaseTest {

  @Autowired private TenderController controller;

  @MockitoBean private TenderRepository tenderRepository;

  @MockitoBean private MongoClient mongoClient;

  @Test
  void contextLoads() {
    assertThat(controller, is(notNullValue()));
  }

  /** Test creating a tender with a file. */
  @Test
  public void createTender() throws Exception {
    // Arrange
    HttpHeaders identityHttpHeaders = TestUtils.createTestHttpHeaders();
    MultiValueMap<String, String> params = TestUtils.createTenderParams();
    MockMultipartFile multipartFile = TestUtils.createMockFile("test.txt", "Spring Framework");

    Tender tender = createTenderFromParams(params, identityHttpHeaders);
    tender.setId(UUID.randomUUID().toString());
    Mockito.when(tenderRepository.save(any(Tender.class))).thenReturn(tender);

    // Act
    MvcResult mvcResult =
        mvc.perform(
                multipart("/tenders")
                    .file(multipartFile)
                    .params(params)
                    .headers(identityHttpHeaders))
            .andExpect(status().isCreated())
            .andReturn();

    // Assert
    TenderDTO createdTender =
        objectMapper.readValue(mvcResult.getResponse().getContentAsByteArray(), TenderDTO.class);

    List<FileMetadataDTO> fileMetadata = createdTender.getFiles();
    assertThat(fileMetadata, is(not(empty())));
    assertThat(fileMetadata, hasSize(1));
    FileMetadataDTO fileMetadataDTO = fileMetadata.getFirst();
    assertThat(fileMetadataDTO.getFileName(), is(equalTo(multipartFile.getOriginalFilename())));
    assertThat(fileMetadataDTO.getFileSizeBytes(), is(equalTo(multipartFile.getSize())));

    assertThat(createdTender.getCreatedBy(), is(notNullValue()));
    UserDTO authUser = createdTender.getCreatedBy();
    assertThat(
        authUser.getFullName(),
        is(
            equalTo(
                identityHttpHeaders.getFirst(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_NAME))));
    assertThat(
        authUser.getUserId(),
        is(
            equalTo(
                identityHttpHeaders.getFirst(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_ID))));
  }

  /**
   * Creates a Tender object from request parameters and HTTP headers.
   *
   * @param params Request parameters containing tender data
   * @param httpHeaders HTTP headers containing user information
   * @return A Tender object populated with data from the parameters
   */
  private static Tender createTenderFromParams(
      MultiValueMap<String, String> params, HttpHeaders httpHeaders) {
    Tender tender = new Tender();
    tender.setTitle(params.getFirst("title"));
    tender.setDescription(params.getFirst("description"));
    tender.setSourceUrl(params.getFirst("sourceUrl"));
    tender.setClient(params.getFirst("client"));
    tender.setSubmissionDate(params.getFirst("submissionDate"));
    tender.setBindingDeadline(params.getFirst("bindingDeadline"));
    tender.setContractDuration(params.getFirst("contractDuration"));
    tender.setPublicationDate(params.getFirst("publicationDate"));
    tender.setQuestionDeadline(params.getFirst("questionDeadline"));
    tender.setWinningCriteria(params.getFirst("winningCriteria"));
    tender.setWeightingPriceQuality(params.getFirst("weightingPriceQuality"));
    tender.setDeliveryLocation(params.getFirst("deliveryLocation"));
    tender.setCreatedBy(
        new User(
            httpHeaders.getFirst(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_ID),
            httpHeaders.getFirst(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_NAME)));
    return tender;
  }
}
