package com.gofore.aita;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gofore.aita.core.data.SystemConfigurationRepository;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FullyQualifiedAnnotationBeanNameGenerator;
import org.springframework.context.annotation.Profile;

@SpringBootApplication
@ComponentScan(nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class)
public class Application {

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }

  @Bean
  @Profile("!test")
  CommandLineRunner initConfig(SystemConfigurationRepository systemConfigurationRepository) {
    return args -> {
      if (systemConfigurationRepository.count() == 0) {
        SystemConfiguration systemConfiguration = SystemConfiguration.defaultConfiguration();
        systemConfigurationRepository.save(systemConfiguration);
      }
    };
  }

  @Bean
  public ObjectMapper objectMapper() {
    return new ObjectMapper().registerModule(new JavaTimeModule());
  }
}
