package com.gofore.aita.core.domain;

import com.gofore.aita.core.data.SystemConfigurationRepository;
import com.gofore.aita.core.domain.mapper.SystemConfigurationUpdateMapper;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class SystemConfigurationService {

  private final SystemConfigurationRepository systemConfigurationRepository;
  private final SystemConfigurationUpdateMapper systemConfigurationUpdateMapper;

  public SystemConfigurationService(
      SystemConfigurationRepository systemConfigurationRepository,
      SystemConfigurationUpdateMapper systemConfigurationUpdateMapper) {
    this.systemConfigurationRepository = systemConfigurationRepository;
    this.systemConfigurationUpdateMapper = systemConfigurationUpdateMapper;
  }

  public SystemConfiguration get() {
    SystemConfiguration result = SystemConfiguration.defaultConfiguration();

    Optional<SystemConfiguration> maybeDb =
        systemConfigurationRepository.findAll().stream().findFirst();
    if (maybeDb.isPresent()) {
      SystemConfiguration db = maybeDb.get();

      if (StringUtils.isNotBlank(db.getAiAnalysisSystemPrompt())) {
        result.setAiAnalysisSystemPrompt(db.getAiAnalysisSystemPrompt());
      }
      if (StringUtils.isNotBlank(db.getAiAnalysisAnalysisPrompt())) {
        result.setAiAnalysisAnalysisPrompt(db.getAiAnalysisAnalysisPrompt());
      }
      if (StringUtils.isNotBlank(db.getAiStructuredOutputPrompt())) {
        result.setAiStructuredOutputPrompt(db.getAiStructuredOutputPrompt());
      }
    }
    return result;
  }

  public SystemConfiguration update(SystemConfiguration updatedConfiguration) {
    SystemConfiguration existing =
        systemConfigurationRepository.findAll().stream()
            .findFirst()
            .orElse(SystemConfiguration.defaultConfiguration());

    systemConfigurationUpdateMapper.updatedConfiguration(updatedConfiguration, existing);

    return systemConfigurationRepository.save(existing);
  }
}
