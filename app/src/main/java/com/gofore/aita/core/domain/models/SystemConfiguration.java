package com.gofore.aita.core.domain.models;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "config")
public class SystemConfiguration {
  private static final String AI_ANALYSIS_SYSTEM_PROMPT =
      "You are an AI assistant " + "that supports us in analysing tenders.";
  private static final String AI_ANALYSIS_ANALYSIS_PROMPT =
      "You will receive a description and documents of the tender. "
          + "Please try to generate a short report highlighting the most salient points.";
  private static final String AI_STRUCTURED_OUTPUT_PROMPT =
      "You are a tender-extraction service. Read the following text and output **only** a single JSON object (no markdown, no code fences, no extra commentary) with exactly these fields and in this order:\n"
          + "  client (issuer of this tender offer): string | null,\n"
          + "  submissionDate (Exact date by which the entire delivery package must be delivered.): string | null,\n"
          + "  bindingDeadline (The binding deadline is the period during which a bidder is legally bound by their offer. Within this period, the offer cannot be modified or withdrawn.): string | null,\n"
          + "  contractDuration (How long the tender offer will last if contract awarded.): string | null,\n"
          + "  publicationDate: string | null,\n"
          + "  questionDeadline: string | null,\n"
          + "  contractValue: number | null,\n"
          + "  maximumBudget (The maximum amount listed in person-days (PT).): number | null,\n"
          + "  winningCriteria (List of criteria that are especially crucial for awarding the contract.): string | null,\n"
          + "  weightingPriceQuality (Weighting of price versus quality.): string | null,\n"
          + "  deliveryLocation: string | null\n\n"
          + "Field requirements:\n"
          + "- Dates must use ISO 8601 (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)\n"
          + "- Numeric fields (contractValue, maximumBudget) should be plain numbers (no currency symbols).\n"
          + "- If a field cannot be determined, set its value to null.";

  @Id private String id;
  private String aiAnalysisSystemPrompt;
  private String aiAnalysisAnalysisPrompt;
  private String aiStructuredOutputPrompt;

  public static SystemConfiguration defaultConfiguration() {
    SystemConfiguration systemConfiguration = new SystemConfiguration();
    systemConfiguration.setAiAnalysisSystemPrompt(AI_ANALYSIS_SYSTEM_PROMPT);
    systemConfiguration.setAiAnalysisAnalysisPrompt(AI_ANALYSIS_ANALYSIS_PROMPT);
    systemConfiguration.setAiStructuredOutputPrompt(AI_STRUCTURED_OUTPUT_PROMPT);
    return systemConfiguration;
  }
}
