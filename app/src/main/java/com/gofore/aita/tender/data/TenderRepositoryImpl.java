package com.gofore.aita.tender.data;

import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import org.bson.Document;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
public class TenderRepositoryImpl implements TenderRepositoryCustom {
  private final MongoTemplate mongoTemplate;

  public TenderRepositoryImpl(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @Override
  public FileMetadata addFileAtomic(String tenderId, FileMetadata fileMetadata, User user) {
    // duplicate check
    Query query = new Query();
    query.addCriteria(
        Criteria.where("_id")
            .is(tenderId)
            .and("files.slugifiedFileName")
            .ne(fileMetadata.getSlugifiedFileName()));

    Update update =
        new Update()
            .push("files", fileMetadata)
            .set("lastUpdatedTime", OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString())
            .set("lastUpdatedBy", user);

    Tender updatedTender =
        mongoTemplate.findAndModify(
            query, update, FindAndModifyOptions.options().returnNew(true), Tender.class);

    return (updatedTender != null) ? fileMetadata : null;
  }

  @Override
  public boolean deleteFileAtomic(String tenderId, String fileId, User user) {
    Query query = new Query();
    query.addCriteria(Criteria.where("_id").is(tenderId).and("files.id").is(fileId));

    Update update =
        new Update()
            .pull("files", new Document("id", fileId))
            .set("lastUpdatedTime", OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString())
            .set("lastUpdatedBy", user);

    Tender updatedTender =
        mongoTemplate.findAndModify(
            query, update, FindAndModifyOptions.options().returnNew(true), Tender.class);

    return updatedTender != null;
  }
}
