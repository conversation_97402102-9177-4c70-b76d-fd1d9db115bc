package com.gofore.aita.tender.domain.models;

import lombok.Getter;

@Getter
public enum WorkflowStatus {
  NEW("New"),
  IN_WORK("In-Work"),
  DECLINED("Declined"),
  OFFERED("Offered"),
  DONE("Done");

  private final String displayName;

  WorkflowStatus(String displayName) {
    this.displayName = displayName;
  }

  public static WorkflowStatus fromValue(String value) {
    for (WorkflowStatus status : values()) {
      if (status.displayName.equalsIgnoreCase(value)) {
        return status;
      }
    }
    throw new IllegalArgumentException("Unsupported WorkflowStatus: " + value);
  }
}
