package com.gofore.aita.tender.api.dto;

import com.gofore.aita.tender.api.Constants;
import com.gofore.aita.tender.domain.models.WorkflowStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class TenderUpdateDTO {

  @NotBlank private String title;

  @NotBlank private String sourceUrl;

  @NotBlank private String client;

  @Pattern(regexp = Constants.REGEX_ISO8601_DATETIME)
  @NotBlank
  private String submissionDate;

  @NotBlank private String bindingDeadline;

  @NotBlank private String contractDuration;

  @Pattern(regexp = Constants.REGEX_ISO8601_DATE)
  @NotBlank
  private String publicationDate;

  @Pattern(regexp = Constants.REGEX_ISO8601_DATETIME)
  @NotBlank
  private String questionDeadline;

  private Float contractValue;

  private String maximumBudget;

  @NotBlank private String winningCriteria;

  @NotBlank private String weightingPriceQuality;

  @NotBlank private String deliveryLocation;

  @NotBlank private String description;

  private WorkflowStatus workflowStatus;

  // User evaluation fields
  private String comment;
  private Integer rating;
  private Boolean isFavorite;
}
