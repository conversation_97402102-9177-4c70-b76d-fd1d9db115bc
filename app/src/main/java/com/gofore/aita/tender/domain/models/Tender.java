package com.gofore.aita.tender.domain.models;

import com.gofore.aita.tender.domain.User;
import java.util.List;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "tenders")
public class Tender {
  @Id private String id;
  private String title;
  private String sourceUrl;
  private String client;
  private String submissionDate;
  private String bindingDeadline;
  private String contractDuration;
  private String publicationDate;
  private String questionDeadline;
  private Float contractValue;
  private String maximumBudget;
  private String winningCriteria;
  private String weightingPriceQuality;
  private String deliveryLocation;
  private String description;
  private List<FileMetadata> files;
  private AnalysisResult analysisResult;

  // User evaluation fields
  private String comment;
  private Integer rating = 0;
  private Boolean isFavorite = Boolean.FALSE;

  // Lifecycle
  private TenderStatus status = TenderStatus.NEW;

  // Workflow
  private WorkflowStatus workflowStatus = WorkflowStatus.NEW;

  // System-generated information
  private String creationTime;
  private User createdBy;
  private String lastUpdatedTime;
  private User lastUpdatedBy;

  @Version private Long version;
}
