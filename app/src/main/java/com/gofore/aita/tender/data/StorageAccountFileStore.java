package com.gofore.aita.tender.data;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.AzureCliCredentialBuilder;
import com.azure.identity.ManagedIdentityCredentialBuilder;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.specialized.BlockBlobClient;
import com.gofore.aita.tender.api.util.FileNameHelper;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

/** Class is responsible to manage files in an Azure Storage Account. */
@Component
@Profile("!test")
public class StorageAccountFileStore implements IFileStore {

  private final Environment environment;
  private final BlobContainerClient blobContainerClient;

  public StorageAccountFileStore(
      @Value("${app.storage.storage-account-name}") String storageAccountName,
      @Value("${app.storage.document-container-name}") String containerName,
      @Value("${app.identity.managed-identity.client-id}") String managedIdentityClientId,
      Environment environment) {
    this.environment = environment;
    BlobServiceClient blobServiceClient =
        new BlobServiceClientBuilder()
            .endpoint("https://%s.blob.core.windows.net/".formatted(storageAccountName))
            .credential(generateCredential(managedIdentityClientId))
            .buildClient();
    blobContainerClient = blobServiceClient.getBlobContainerClient(containerName);
    // ensure that Container will be created if it does not exist
    blobContainerClient.createIfNotExists();
  }

  /**
   * Generate credential which is required to perform requests against Storage Account. In the Cloud
   * environment a Managed Identity is used to generate credentials, whereas in the local
   * environment the Azure CLI can be used to generate credentials. The method automatically
   * evalutes which option to use.
   *
   * @param managedIdentityClientId Client ID of the managed identity to be used
   * @return
   */
  private TokenCredential generateCredential(String managedIdentityClientId) {
    for (String profile : environment.getActiveProfiles()) {
      if (profile.equals("dev")) {
        return new AzureCliCredentialBuilder().build();
      }
    }
    return new ManagedIdentityCredentialBuilder().clientId(managedIdentityClientId).build();
  }

  @Override
  public Pair<String, String> storeFile(
      String folderName, String fileName, InputStream fileStream, long fileSizeBytes) {
    String uuid = UUID.randomUUID().toString();
    String fixedFilename = FileNameHelper.fixUtf8FilenameBug(fileName);
    String slugifiedName = FileNameHelper.getSlugifiedName(fixedFilename);
    String storageFileName = uuid + "_" + slugifiedName;
    String filePath = "%s/%s".formatted(folderName, storageFileName);
    BlockBlobClient blockBlobClient =
        blobContainerClient.getBlobClient(filePath).getBlockBlobClient();

    // TODO File upload kann ggf. optimiert werden, z.B. concurrency
    BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);
    blockBlobClient.upload(bufferedInputStream, fileSizeBytes);
    return Pair.of(slugifiedName, filePath);
  }

  @Override
  public void deleteFile(String filePath) {
    BlobClient blobClient = blobContainerClient.getBlobClient(filePath);
    blobClient.deleteIfExists();
  }

  @Override
  public InputStream retrieveFile(String filePath) {
    BlobClient blobClient = blobContainerClient.getBlobClient(filePath);
    return blobClient.openInputStream();
  }
}
