package com.gofore.aita.analysis.domain;

import com.gofore.aita.analysis.data.ApiManagementAIService;
import com.gofore.aita.analysis.data.OpenAIService;
import com.gofore.aita.core.domain.SystemConfigurationService;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import com.gofore.aita.extraction.domain.FileExtractionService;
import com.gofore.aita.extraction.domain.TextProcessingService;
import com.gofore.aita.extraction.domain.TextProcessingService.TextWithTokenCount;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.exceptions.ResourceNotFoundException;
import com.gofore.aita.tender.domain.mapper.AnalysisResultMapper;
import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.TenderStatus;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenderAnalysisService {

  private final TenderRepository tenderRepository;
  private final FileExtractionService fileExtraction;
  private final TextProcessingService textProcessing;
  private final SystemConfigurationService configService;
  private final OpenAIService openAIService;
  private final ApiManagementAIService apimService;
  private final AnalysisResultMapper resultMapper;

  /**
   * Analyzes a tender, processing the text, and persisting the result.
   *
   * @param tenderId The ID of the tender to analyze
   * @return The analysis result
   * @throws ResourceNotFoundException if the tender is not found
   */
  @Transactional
  public com.gofore.aita.tender.domain.models.AnalysisResult analyzeTender(String tenderId, String model) {
    log.info("Starting analysis for tender {}", tenderId);

    Tender tender =
        tenderRepository
            .findById(tenderId)
            .orElseThrow(() -> new ResourceNotFoundException("Tender not found: " + tenderId));

    tender.setStatus(TenderStatus.ANALYZING);
    tender = tenderRepository.save(tender);

    // extract or use extracted document text
    List<ExtractedDocument> allDocs = fileExtraction.processFilesForExtraction(tender);

    // filter out any “forms”
    List<ExtractedDocument> docs =
        allDocs.stream()
            .filter(doc -> doc.getIsForm() == null || !doc.getIsForm())
            .collect(Collectors.toList());

    // build the full prompt text + token count
    String description = tender.getDescription();
    TextWithTokenCount textWithTokens = textProcessing.combineTexts(description, docs);
    log.debug("Total tokens: {}", textWithTokens.getTokenCount());

    // run the analysis
    SystemConfiguration cfg = configService.get();

    com.gofore.aita.analysis.models.AnalysisResult raw;
    if (StringUtils.isNotBlank(model)) {
      raw = apimService.analyzeTender(textWithTokens.getText(), cfg.getAiAnalysisAnalysisPrompt(), model);
    } else {
      raw = openAIService.analyzeTender(textWithTokens.getText(), cfg.getAiAnalysisAnalysisPrompt());
    }

    // map, persist, and return
    com.gofore.aita.tender.domain.models.AnalysisResult out = resultMapper.map(raw);
    out.setLastUpdatedTime(OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString());
    tender.setAnalysisResult(out);
    tender.setStatus(TenderStatus.ANALYZED);
    tenderRepository.save(tender);

    log.info("Completed analysis for tender {}", tenderId);
    return out;
  }
}
