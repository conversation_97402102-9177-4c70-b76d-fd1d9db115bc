package com.gofore.aita.extraction.utils;

import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for counting tokens in text. This implementation uses a simple approximation based
 * on word count, which is suitable for estimating token counts for OpenAI models.
 */
@UtilityClass
public class TokenCounter {

  private static final Logger logger = LoggerFactory.getLogger(TokenCounter.class);
  // GPT models typically use ~1.3 tokens per word for English text
  private static final double TOKENS_PER_WORD = 1.3;

  /**
   * Estimates the number of tokens in the given text. This is an approximation suitable for
   * planning purposes.
   *
   * @param text The text to count tokens in
   * @return Estimated token count
   */
  public static int countTokens(String text) {
    if (text == null || text.isEmpty()) {
      return 0;
    }

    // split by whitespace to count words
    String[] words = text.trim().split("\\s+");

    // apply the token-per-word ratio and round to an integer
    int estimatedTokens = (int) Math.ceil(words.length * TOKENS_PER_WORD);

    logger.debug("Estimated {} tokens for {} words of text", estimatedTokens, words.length);
    return estimatedTokens;
  }
}
